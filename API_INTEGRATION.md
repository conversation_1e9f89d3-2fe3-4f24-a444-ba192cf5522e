# API Integration Documentation

This document describes the API integration implemented for the JQ Gaming Verse UI application.

## Overview

The application now integrates with three main API endpoints:
1. `/game` - Fetch game data
2. `/user-stats` - Get user statistics for a specific game
3. `/update-score` - Update user score after gameplay

## Environment Configuration

### Required Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:5000
VITE_API_KEY=your-api-key-here
```

### API Authentication

All API requests include the `x-api-key` header for authentication. The API key should be obtained from your backend team and configured in the environment variables.

## Architecture

### 1. API Client (`src/lib/api-client.ts`)
- Centralized HTTP client with authentication
- Error handling with custom `ApiClientError` class
- Support for GET, POST, PUT, DELETE methods

### 2. API Types (`src/types/api.ts`)
- TypeScript interfaces for all API requests and responses
- Consistent typing across the application

### 3. Service Layer (`src/services/game-api.ts`)
- High-level API service methods
- Business logic abstraction
- Error handling and logging

### 4. React Query Hooks (`src/hooks/use-game-api.ts`)
- Data fetching with caching
- Loading and error states
- Optimistic updates for mutations

### 5. Context Management (`src/contexts/GameContext.tsx`)
- Global state for game data and Telegram ID
- Persistent storage for user preferences
- Modal state management

## Components

### 1. Games Page Integration
- Fetches games from `/game` endpoint
- Loading and error states
- Game selection with context updates

### 2. GamePlay Page Integration
- Telegram ID modal for user identification
- User stats fetching from `/user-stats`
- Score updates via `/update-score`
- Real-time score tracking

### 3. Telegram ID Modal
- User-friendly input validation
- Persistent storage of Telegram ID
- Error handling and feedback

## Usage

### Testing the Integration

1. **Console Testing**: Open browser console and run:
```javascript
// Test all APIs
ApiTestUtils.runAllTests('gameId', 'telegramId');

// Test individual endpoints
ApiTestUtils.testGamesApi();
ApiTestUtils.testUserStatsApi('gameId', 'telegramId');
ApiTestUtils.testUpdateScoreApi('gameId', 1000, 'telegramId');
```

2. **Manual Testing**:
   - Navigate to `/games` to test game fetching
   - Click on a game to enter GamePlay
   - Enter Telegram ID when prompted
   - Play a game to test score updates

### Error Handling

The application handles various error scenarios:
- Network connectivity issues
- Invalid API keys
- Server errors (4xx, 5xx)
- Missing Telegram ID
- Invalid game data

### Data Flow

1. **Games Page**:
   ```
   Component Mount → useGames Hook → API Call → Display Games
   ```

2. **GamePlay Page**:
   ```
   Component Mount → Check Telegram ID → Show Modal (if needed)
   → Fetch User Stats → Display Stats
   → Game End → Update Score → Refresh Stats
   ```

## API Endpoints

### GET /game
Fetches all available games.

**Response:**
```json
{
  "message": "Games fetched successfully",
  "payload": {
    "games": [
      {
        "_id": "game_id",
        "title": "Game Title",
        "description": "Game description",
        "image": "🎮",
        "blockchain": "BSC",
        "category": "Puzzle",
        "min_reward": 50,
        "max_reward": 200,
        "difficulty": "Easy",
        "featured": true,
        "trending": false
      }
    ]
  }
}
```

### POST /user-stats
Fetches user statistics for a specific game.

**Request:**
```json
{
  "game_id": "game_id",
  "tg_id": "telegram_id"
}
```

**Response:**
```json
{
  "message": "User stats fetched successfully",
  "payload": {
    "best_score": 1500,
    "games_played": 10,
    "total_score": 8500
  }
}
```

### POST /update-score
Updates user score after gameplay.

**Request:**
```json
{
  "game_id": "game_id",
  "score": 1000,
  "tg_id": "telegram_id"
}
```

**Response:**
```json
{
  "message": "Score updated successfully",
  "payload": {
    "best_score": 1500,
    "games_played": 11,
    "total_score": 9500
  }
}
```

## Troubleshooting

### Common Issues

1. **API Key Missing**: Ensure `VITE_API_KEY` is set in `.env`
2. **CORS Issues**: Backend must allow requests from frontend domain
3. **Network Errors**: Check API base URL and server availability
4. **Telegram ID Issues**: Modal should appear automatically if not set

### Debug Mode

Enable debug logging by setting:
```javascript
localStorage.setItem('debug', 'api');
```

This will log all API requests and responses to the console.
