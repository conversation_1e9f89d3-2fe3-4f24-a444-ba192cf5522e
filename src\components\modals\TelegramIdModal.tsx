import React, { useState, useEffect } from 'react';
import { X, User, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useGame } from '@/contexts/GameContext';

interface TelegramIdModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (telegramId: string) => void;
}

export const TelegramIdModal: React.FC<TelegramIdModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [telegramId, setTelegramId] = useState('');
  const [error, setError] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const { telegramId: storedTelegramId } = useGame();

  // Pre-fill with stored Telegram ID if available
  useEffect(() => {
    if (storedTelegramId) {
      setTelegramId(storedTelegramId);
    }
  }, [storedTelegramId]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setError('');
      setIsValidating(false);
    }
  }, [isOpen]);

  const validateTelegramId = (id: string): boolean => {
    // Basic validation for Telegram ID
    // Telegram IDs are typically numeric strings
    const trimmedId = id.trim();
    
    if (!trimmedId) {
      setError('Telegram ID is required');
      return false;
    }

    if (trimmedId.length < 5) {
      setError('Telegram ID must be at least 5 characters long');
      return false;
    }

    // Check if it's a valid format (can be numeric or username)
    const isNumeric = /^\d+$/.test(trimmedId);
    const isUsername = /^[a-zA-Z0-9_]{5,32}$/.test(trimmedId);

    if (!isNumeric && !isUsername) {
      setError('Invalid Telegram ID format. Use your numeric ID or username.');
      return false;
    }

    setError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateTelegramId(telegramId)) {
      return;
    }

    setIsValidating(true);
    
    try {
      // Here you could add additional validation by calling Telegram API
      // For now, we'll just validate the format
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
      
      onSubmit(telegramId.trim());
      onClose();
    } catch (error) {
      setError('Failed to validate Telegram ID. Please try again.');
    } finally {
      setIsValidating(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTelegramId(value);
    
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-card-dark border border-border-light rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-accent-cyan/10 rounded-lg">
              <User className="h-5 w-5 text-accent-cyan" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-primary-text">
                Enter Telegram ID
              </h2>
              <p className="text-sm text-secondary-text">
                Required to track your game progress
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-secondary-text hover:text-primary-text"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="telegram-id" className="text-primary-text">
              Telegram ID or Username
            </Label>
            <Input
              id="telegram-id"
              type="text"
              value={telegramId}
              onChange={handleInputChange}
              placeholder="e.g., 123456789 or @username"
              className="bg-surface-dark border-border-light text-primary-text placeholder:text-muted-text"
              disabled={isValidating}
            />
            
            {/* Error message */}
            {error && (
              <div className="flex items-center space-x-2 text-sm text-red-400">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </div>
            )}
            
            {/* Success message */}
            {!error && telegramId && validateTelegramId(telegramId) && (
              <div className="flex items-center space-x-2 text-sm text-green-400">
                <CheckCircle className="h-4 w-4" />
                <span>Valid Telegram ID format</span>
              </div>
            )}
          </div>

          {/* Help text */}
          <div className="bg-surface-dark/50 rounded-lg p-3 text-sm text-secondary-text">
            <p className="font-medium mb-1">How to find your Telegram ID:</p>
            <ul className="space-y-1 text-xs">
              <li>• Message @userinfobot on Telegram</li>
              <li>• Use your username (e.g., @yourname)</li>
              <li>• Or use your numeric ID</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isValidating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-r from-accent-cyan to-xp-purple"
              disabled={isValidating || !telegramId.trim()}
            >
              {isValidating ? 'Validating...' : 'Continue'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
