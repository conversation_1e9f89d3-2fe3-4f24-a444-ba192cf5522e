import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GameApiService } from '@/services/game-api';
import { Game, UserStats } from '@/types/api';

// Query Keys
export const QUERY_KEYS = {
  GAMES: ['games'] as const,
  USER_STATS: (gameId: string, telegramId: string) => ['user-stats', gameId, telegramId] as const,
} as const;

// Hook to fetch games
export const useGames = () => {
  return useQuery({
    queryKey: QUERY_KEYS.GAMES,
    queryFn: GameApiService.getGames,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  });
};

// Hook to fetch user stats
export const useUserStats = (gameId: string, telegramId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.USER_STATS(gameId, telegramId),
    queryFn: () => GameApiService.getUserStats(gameId, telegramId),
    enabled: enabled && !!gameId && !!telegramId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook to update score
export const useUpdateScore = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ gameId, score, telegramId }: { gameId: string; score: number; telegramId: string }) =>
      GameApiService.updateScore(gameId, score, telegramId),
    onSuccess: (data, variables) => {
      // Update the user stats cache with the new data
      queryClient.setQueryData(
        QUERY_KEYS.USER_STATS(variables.gameId, variables.telegramId),
        data
      );
      
      // Optionally invalidate to refetch from server
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.USER_STATS(variables.gameId, variables.telegramId),
      });
    },
    onError: (error) => {
      console.error('Failed to update score:', error);
    },
  });
};
