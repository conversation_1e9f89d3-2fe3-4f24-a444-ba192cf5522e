// API Integration Test Utilities
// This file contains utilities to test the API integration

import { GameApiService } from '@/services/game-api';
import { ApiClientError } from '@/lib/api-client';

export class ApiTestUtils {
  /**
   * Test the games API endpoint
   */
  static async testGamesApi(): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      console.log('🧪 Testing Games API...');
      const games = await GameApiService.getGames();
      console.log('✅ Games API test successful:', games);
      return { success: true, data: games };
    } catch (error) {
      console.error('❌ Games API test failed:', error);
      return { 
        success: false, 
        error: error instanceof ApiClientError ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Test the user stats API endpoint
   */
  static async testUserStatsApi(gameId: string, telegramId: string): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      console.log('🧪 Testing User Stats API...');
      const stats = await GameApiService.getUserStats(gameId, telegramId);
      console.log('✅ User Stats API test successful:', stats);
      return { success: true, data: stats };
    } catch (error) {
      console.error('❌ User Stats API test failed:', error);
      return { 
        success: false, 
        error: error instanceof ApiClientError ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Test the update score API endpoint
   */
  static async testUpdateScoreApi(gameId: string, score: number, telegramId: string): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      console.log('🧪 Testing Update Score API...');
      const result = await GameApiService.updateScore(gameId, score, telegramId);
      console.log('✅ Update Score API test successful:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('❌ Update Score API test failed:', error);
      return { 
        success: false, 
        error: error instanceof ApiClientError ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Run all API tests
   */
  static async runAllTests(gameId: string = '1', telegramId: string = 'test123'): Promise<void> {
    console.log('🚀 Starting API Integration Tests...');
    
    // Test Games API
    const gamesTest = await this.testGamesApi();
    
    // Test User Stats API
    const statsTest = await this.testUserStatsApi(gameId, telegramId);
    
    // Test Update Score API
    const scoreTest = await this.testUpdateScoreApi(gameId, 1000, telegramId);
    
    // Summary
    console.log('\n📊 Test Results Summary:');
    console.log(`Games API: ${gamesTest.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`User Stats API: ${statsTest.success ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Update Score API: ${scoreTest.success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (!gamesTest.success) console.log(`  - Games API Error: ${gamesTest.error}`);
    if (!statsTest.success) console.log(`  - User Stats API Error: ${statsTest.error}`);
    if (!scoreTest.success) console.log(`  - Update Score API Error: ${scoreTest.error}`);
  }
}

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).ApiTestUtils = ApiTestUtils;
}
