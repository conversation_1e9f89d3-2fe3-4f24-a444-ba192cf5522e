/* Enhanced JargonQuest Dashboard Styles */

.dashboardHeader {
  position: relative;
  overflow: hidden;
}

/* Enhanced metric cards */
.metric-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 1.5rem;
  padding: 2rem 1.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1);
  animation: fade-in-up 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.metric-card:hover {
  border-color: rgba(6, 182, 212, 0.5);
  box-shadow: 
    0 20px 40px rgba(6, 182, 212, 0.2),
    0 0 30px rgba(139, 92, 246, 0.1);
  transform: translateY(-8px) scale(1.03);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.1) 0%, 
    rgba(139, 92, 246, 0.05) 50%, 
    rgba(245, 158, 11, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.5s ease;
  border-radius: inherit;
  pointer-events: none;
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(6, 182, 212, 0.1), 
    transparent);
  transition: left 0.6s ease;
  pointer-events: none;
}

.metric-card:hover::after {
  left: 100%;
}

/* Enhanced metric numbers */
.metric-number {
  font-size: 2.5rem;
  font-weight: 900;
  font-family: 'JetBrains Mono', monospace;
  background: linear-gradient(135deg, #06B6D4 0%, #8B5CF6 50%, #F59E0B 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease-in-out infinite, number-glow 2s ease-in-out infinite alternate;
  margin-bottom: 0.5rem;
  position: relative;
}

.metric-number::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #06B6D4 0%, #8B5CF6 50%, #F59E0B 100%);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease-in-out infinite;
  filter: blur(15px);
  opacity: 0.3;
  z-index: -1;
}

/* Enhanced metric labels */
.metric-label {
  font-size: 0.875rem;
  color: #CBD5E1;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: color 0.3s ease;
  margin-top: 0.5rem;
}

.metric-card:hover .metric-label {
  color: #F8FAFC;
}

/* Tab enhancements */
.tab-trigger {
  position: relative;
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  color: #94A3B8;
  border: 1px solid transparent;
}

.tab-trigger:hover {
  color: #CBD5E1;
  background: rgba(51, 65, 85, 0.5);
}

.tab-trigger[data-state="active"] {
  background: linear-gradient(135deg, #06B6D4 0%, #8B5CF6 100%);
  color: white;
  border-color: rgba(6, 182, 212, 0.3);
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.4);
}

.tab-trigger[data-state="active"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #06B6D4 0%, #8B5CF6 100%);
  border-radius: inherit;
  filter: blur(10px);
  opacity: 0.5;
  z-index: -1;
}

/* Wallet info card */
.wallet-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 1.5rem;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.wallet-card:hover {
  border-color: rgba(6, 182, 212, 0.4);
  box-shadow: 0 10px 25px rgba(6, 182, 212, 0.1);
}

.wallet-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(6, 182, 212, 0.05) 0%, 
    rgba(139, 92, 246, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  pointer-events: none;
}

.wallet-card:hover::before {
  opacity: 1;
}

/* Progress bars enhancement */
.progress-bar {
  height: 0.75rem;
  background: rgba(51, 65, 85, 0.8);
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: inherit;
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  animation: shimmer-progress 2s linear infinite;
}

/* Activity feed */
.activity-item {
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 1rem;
  padding: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.activity-item:hover {
  background: rgba(51, 65, 85, 0.6);
  border-color: rgba(6, 182, 212, 0.3);
  transform: translateX(4px);
}

.activity-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #06B6D4 0%, #8B5CF6 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-item:hover::before {
  opacity: 1;
}

/* Leaderboard enhancements */
.leaderboard-item {
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.leaderboard-item:hover {
  background: rgba(51, 65, 85, 0.6);
  border-color: rgba(6, 182, 212, 0.3);
  transform: translateY(-2px) scale(1.02);
}

.leaderboard-item.top-three {
  background: linear-gradient(135deg, 
    rgba(245, 158, 11, 0.1) 0%, 
    rgba(249, 115, 22, 0.05) 100%);
  border-color: rgba(245, 158, 11, 0.3);
}

.leaderboard-item.top-three::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(245, 158, 11, 0.05) 0%, 
    rgba(249, 115, 22, 0.03) 100%);
  border-radius: inherit;
  pointer-events: none;
}

.rank-badge {
  width: 3rem;
  height: 3rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  font-size: 1.125rem;
  position: relative;
  transition: all 0.3s ease;
}

.rank-badge.first {
  background: linear-gradient(135deg, #F59E0B 0%, #F97316 100%);
  color: #0F172A;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.4);
}

.rank-badge.second {
  background: linear-gradient(135deg, #94A3B8 0%, #64748B 100%);
  color: #0F172A;
  box-shadow: 0 0 20px rgba(148, 163, 184, 0.4);
}

.rank-badge.third {
  background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
  color: #0F172A;
  box-shadow: 0 0 20px rgba(249, 115, 22, 0.4);
}

/* Animations */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-shift {
  0%, 100% { 
    background-position: 0% 50%; 
  }
  50% { 
    background-position: 100% 50%; 
  }
}

@keyframes number-glow {
  0% { 
    text-shadow: 
      0 0 10px rgba(6, 182, 212, 0.3),
      0 0 20px rgba(6, 182, 212, 0.2);
  }
  100% { 
    text-shadow: 
      0 0 20px rgba(139, 92, 246, 0.4),
      0 0 30px rgba(139, 92, 246, 0.3);
  }
}

@keyframes shimmer-progress {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(200%); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 
      0 0 10px rgba(6, 182, 212, 0.3),
      0 0 20px rgba(6, 182, 212, 0.2);
  }
  50% {
    box-shadow: 
      0 0 20px rgba(6, 182, 212, 0.6),
      0 0 40px rgba(6, 182, 212, 0.4);
  }
}

/* Status indicators */
.status-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  position: relative;
}

.status-indicator.online {
  background: #10B981;
  animation: pulse-dot 2s ease-in-out infinite;
}

.status-indicator.offline {
  background: #6B7280;
}

@keyframes pulse-dot {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.5; 
    transform: scale(1.2); 
  }
}

/* Icon container enhancements */
.icon-container {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-container:hover {
  transform: scale(1.1) rotate(5deg);
}

.icon-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  border-radius: inherit;
  filter: blur(10px);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.icon-container:hover::before {
  opacity: 0.5;
}

/* Responsive enhancements */
@media (max-width: 1024px) {
  .metric-card {
    padding: 1.5rem 1rem;
  }
  
  .metric-number {
    font-size: 2rem;
  }
  
  .wallet-card {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .metric-card {
    padding: 1rem;
  }
  
  .metric-number {
    font-size: 1.75rem;
  }
  
  .metric-card:hover {
    transform: translateY(-4px) scale(1.02);
  }
  
  .leaderboard-item:hover,
  .activity-item:hover {
    transform: translateY(-1px);
  }
}

@media (max-width: 640px) {
  .tab-trigger {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .rank-badge {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
  
  .metric-label {
    font-size: 0.75rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .metric-card,
  .metric-number,
  .icon-container,
  .activity-item,
  .leaderboard-item,
  .progress-fill {
    animation: none;
    transition: none;
  }
  
  .metric-card:hover,
  .leaderboard-item:hover,
  .activity-item:hover,
  .icon-container:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .metric-card,
  .wallet-card,
  .activity-item,
  .leaderboard-item {
    border-width: 2px;
    border-color: #06B6D4;
  }
  
  .metric-number {
    -webkit-text-fill-color: #06B6D4;
    color: #06B6D4;
  }
}

/* Focus styles for accessibility */
.metric-card:focus-within,
.activity-item:focus-within,
.leaderboard-item:focus-within,
.tab-trigger:focus {
  outline: 2px solid #06B6D4;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .metric-card,
  .wallet-card,
  .activity-item,
  .leaderboard-item {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
  
  .metric-number {
    -webkit-text-fill-color: #333 !important;
    color: #333 !important;
  }
}