import { apiClient } from '@/lib/api-client';
import {
  API_ENDPOINTS,
  GamesResponse,
  UserStatsRequest,
  UserStatsResponse,
  UpdateScoreRequest,
  UpdateScoreResponse,
} from '@/types/api';

// Game API Service
export class GameApiService {
  /**
   * Fetch all games
   */
  static async getGames() {
    try {
      const response = await apiClient.get<GamesResponse>(API_ENDPOINTS.GAMES);
      return response.payload.games;
    } catch (error) {
      console.error('Error fetching games:', error);
      throw error;
    }
  }

  /**
   * Get user stats for a specific game
   */
  static async getUserStats(gameId: string, telegramId: string) {
    try {
      const requestData: UserStatsRequest = {
        game_id: gameId,
        tg_id: telegramId,
      };

      const response = await apiClient.post<UserStatsResponse>(
        API_ENDPOINTS.USER_STATS,
        requestData
      );

      return response.payload;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }

  /**
   * Update user score for a specific game
   */
  static async updateScore(gameId: string, score: number, telegramId: string) {
    try {
      const requestData: UpdateScoreRequest = {
        game_id: gameId,
        score: score,
        tg_id: telegramId,
      };

      const response = await apiClient.post<UpdateScoreResponse>(
        API_ENDPOINTS.UPDATE_SCORE,
        requestData
      );

      return response.payload;
    } catch (error) {
      console.error('Error updating score:', error);
      throw error;
    }
  }
}

// Export individual functions for easier importing
export const { getGames, getUserStats, updateScore } = GameApiService;
