import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  Wallet,
  Trophy,
  Users,
  TrendingUp,
  Coins,
  Star,
  Target,
  Clock,
  GamepadIcon,
  DollarSign,
  Activity,
  Crown,
  Zap,
  Award,
  BarChart3,
  Shield,
  Sparkles,
  Globe,
  Calendar,
  Timer,
  User
} from 'lucide-react';
import { useWallet } from '@/contexts/WalletContext';
import AnimatedCounter from '@/components/ui/AnimatedCounter';
import styles from './Dashboard.module.css';

const Dashboard = () => {
  const { isConnected, address, balance } = useWallet();
  const [activeTab, setActiveTab] = useState('personal');

  // Mock data
  const personalStats = {
    totalEarned: 1247.85,
    socialPoints: 3420,
    jqPoints: 8950,
    gamesPlayed: 127,
    totalPlayTime: '48h 32m',
    winRate: 78,
    currentRank: 156,
    achievements: 23,
    weeklyEarnings: 156.42,
    streak: 7,
    level: 42,
    nextLevelProgress: 65,
  };

  const communityStats = {
    activePlayers: 156789,
    totalGamesPlayed: 2456789,
    bnbPool: 892456.78,
    weeklyGrowth: 12.5,
    totalRewardsDistributed: 5234567.89,
    averageSessionTime: '24m',
    topPlayers: [
      { name: 'CryptoGamer123', points: 156789, position: 1, avatar: '🏆' },
      { name: 'BlockchainPro', points: 134562, position: 2, avatar: '⚡' },
      { name: 'NFTHunter', points: 123456, position: 3, avatar: '🎯' },
      { name: 'DeFiMaster', points: 98765, position: 4, avatar: '💎' },
      { name: 'Web3Warrior', points: 87654, position: 5, avatar: '🚀' },
    ],
  };

  const platformStats = {
    totalUsers: 456789,
    dailyActiveUsers: 89456,
    totalRewards: 1234567.89,
    gamesAvailable: 45,
    partnerships: 12,
    blockchains: 5,
    monthlyGrowth: 23.5,
    totalTransactions: 2456789,
  };

  const recentActivity = [
    { type: 'reward', description: 'Earned 50 JQ from Crypto Runner Elite', time: '2 mins ago', icon: '💰', color: 'success-green' },
    { type: 'achievement', description: 'Unlocked "Speed Demon" achievement', time: '15 mins ago', icon: '🏆', color: 'gaming-gold' },
    { type: 'game', description: 'Completed level 25 in NFT Puzzle Matrix', time: '1 hour ago', icon: '🎮', color: 'xp-purple' },
    { type: 'social', description: 'Received referral bonus: 25 JQ', time: '3 hours ago', icon: '👥', color: 'accent-cyan' },
    { type: 'milestone', description: 'Reached Player Level 42!', time: '1 day ago', icon: '⭐', color: 'legendary-orange' },
  ];

  if (!isConnected) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30 pointer-events-none"></div>
        <div className="fixed inset-0 digital-grid opacity-10 pointer-events-none"></div>

        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-12 text-center max-w-md mx-auto hover:border-accent-cyan/30 transition-all duration-300">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Wallet className="h-12 w-12 text-accent-cyan" />
              </div>
              <div className="text-6xl mb-4">🔐</div>
            </div>
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Connect Your Wallet
            </h2>
            <p className="text-secondary-text mb-6 leading-relaxed">
              Please connect your wallet to access your gaming dashboard and view your epic stats and achievements.
            </p>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-text">
              <Shield className="w-4 h-4" />
              <span>Secure & Decentralized</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
        <div className="absolute inset-0 digital-grid opacity-5"></div>

        {/* Floating elements */}
        {[...Array(4)].map((_, i) => (
          <div
            key={i}
            className="absolute opacity-5 pointer-events-none"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float-advanced ${8 + Math.random() * 4}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 3}s`,
            }}
          >
            <div
              className="w-32 h-32 rounded-full"
              style={{
                background: `radial-gradient(circle, ${['#06B6D4', '#8B5CF6', '#F59E0B'][i % 3]
                  }15, transparent 70%)`,
              }}
            />
          </div>
        ))}
      </div>

      <div className="relative z-10 pt-24 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Enhanced Header */}
          <div className="mb-12 text-center">
            <div className="mb-6">
              {/* <h1 className="text-4xl md:text-6xl font-black mb-4">
                <span className="bg-gradient-to-r from-accent-cyan via-xp-purple to-gaming-gold bg-clip-text text-transparent">
                  Gaming Dashboard
                </span>
              </h1> */}
              <div className="flex items-center justify-center gap-3 text-xl md:text-2xl font-bold text-accent-cyan mb-4">
                <BarChart3 className="w-6 h-6" />
                <span>Track • Analyze • Dominate</span>
                <Trophy className="w-6 h-6" />
              </div>
            </div>
            <p className="text-lg text-secondary-text max-w-3xl mx-auto leading-relaxed">
              Welcome back, legendary gamer! Track your epic journey, analyze your performance,
              and discover new opportunities to level up your gaming empire.
            </p>
          </div>

          {/* Enhanced Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            <div className="flex justify-center">
              <TabsList className="bg-gradient-to-r from-card-dark/80 to-surface-dark/60 backdrop-blur-xl border border-border-light/30 rounded-2xl p-2">
                <TabsTrigger
                  value="personal"
                  className="px-6 py-3 rounded-xl font-semibold transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-accent-cyan data-[state=active]:to-xp-purple data-[state=active]:text-white"
                >
                  <User className="w-4 h-4 mr-2" />
                  Personal Metrics
                </TabsTrigger>
                <TabsTrigger
                  value="community"
                  className="px-6 py-3 rounded-xl font-semibold transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-accent-cyan data-[state=active]:to-xp-purple data-[state=active]:text-white"
                >
                  <Users className="w-4 h-4 mr-2" />
                  Community
                </TabsTrigger>
                <TabsTrigger
                  value="platform"
                  className="px-6 py-3 rounded-xl font-semibold transition-all duration-300 data-[state=active]:bg-gradient-to-r data-[state=active]:from-accent-cyan data-[state=active]:to-xp-purple data-[state=active]:text-white"
                >
                  <Globe className="w-4 h-4 mr-2" />
                  Platform Stats
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Personal Metrics Tab */}
            <TabsContent value="personal" className="space-y-8">
              {/* Enhanced Wallet Info */}
              <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-6 hover:border-accent-cyan/30 transition-all duration-300">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-xl flex items-center justify-center">
                      <Wallet className="h-6 w-6 text-accent-cyan" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">Wallet Information</h3>
                      <p className="text-secondary-text text-sm">Your connected gaming wallet</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-success-green">
                    <div className="w-2 h-2 bg-success-green rounded-full animate-pulse"></div>
                    <span>Connected</span>
                  </div>
                </div>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-surface-dark/60 rounded-xl p-4">
                    <p className="text-sm text-secondary-text mb-2">Wallet Address</p>
                    <p className="font-mono text-sm bg-deep-space/50 p-3 rounded-lg border border-border-light/20 break-all">
                      {address}
                    </p>
                  </div>
                  <div className="bg-surface-dark/60 rounded-xl p-4">
                    <p className="text-sm text-secondary-text mb-2">Current Balance</p>
                    <div className="flex items-center gap-2">
                      <p className="text-3xl font-bold text-gaming-gold">{balance}</p>
                      <span className="text-lg text-secondary-text">BNB</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Key Metrics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-success-green/20 to-wallet-green/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Activity className="h-6 w-6 text-success-green" />
                  </div>
                  <div className={`${styles['metric-number']} text-success-green text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.dailyActiveUsers} />
                  </div>
                  <div className={styles['metric-label']}>Daily Active</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-gaming-gold/20 to-legendary-orange/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <DollarSign className="h-6 w-6 text-gaming-gold" />
                  </div>
                  <div className={`${styles['metric-number']} text-gaming-gold text-2xl mb-2`}>
                    $<AnimatedCounter value={platformStats.totalRewards} decimals={0} />
                  </div>
                  <div className={styles['metric-label']}>Total Rewards</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-xp-purple/20 to-epic-purple/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <GamepadIcon className="h-6 w-6 text-xp-purple" />
                  </div>
                  <div className={`${styles['metric-number']} text-xp-purple text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.gamesAvailable} />
                  </div>
                  <div className={styles['metric-label']}>Epic Games</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-blue/20 to-rare-blue/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Target className="h-6 w-6 text-primary-blue" />
                  </div>
                  <div className={`${styles['metric-number']} text-primary-blue text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.partnerships} />
                  </div>
                  <div className={styles['metric-label']}>Partnerships</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-accent-cyan/20 to-success-green/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Coins className="h-6 w-6 text-accent-cyan" />
                  </div>
                  <div className={`${styles['metric-number']} text-accent-cyan text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.blockchains} />
                  </div>
                  <div className={styles['metric-label']}>Blockchains</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-legendary-orange/20 to-gaming-gold/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp className="h-6 w-6 text-legendary-orange" />
                  </div>
                  <div className={`${styles['metric-number']} text-legendary-orange text-3xl mb-2`}>
                    +<AnimatedCounter value={platformStats.monthlyGrowth} decimals={1} />%
                  </div>
                  <div className={styles['metric-label']}>Monthly Growth</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-xp-purple/20 to-epic-purple/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Zap className="h-6 w-6 text-xp-purple" />
                  </div>
                  <div className={`${styles['metric-number']} text-xp-purple text-2xl mb-2`}>
                    <AnimatedCounter value={platformStats.totalTransactions} />
                  </div>
                  <div className={styles['metric-label']}>Transactions</div>
                </div>
              </div>

              {/* Platform Overview */}
              <div className="grid md:grid-cols-2 gap-8">
                <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-6 hover:border-accent-cyan/30 transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-xl flex items-center justify-center">
                      <BarChart3 className="h-6 w-6 text-accent-cyan" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">Platform Health</h3>
                      <p className="text-secondary-text text-sm">System performance metrics</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-surface-dark/40 rounded-xl">
                      <span className="text-sm text-secondary-text">Server Uptime</span>
                      <span className="text-success-green font-bold">99.9%</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-surface-dark/40 rounded-xl">
                      <span className="text-sm text-secondary-text">Transaction Speed</span>
                      <span className="text-accent-cyan font-bold">2s</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-surface-dark/40 rounded-xl">
                      <span className="text-sm text-secondary-text">Network Status</span>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-success-green rounded-full animate-pulse"></div>
                        <span className="text-success-green font-bold">Optimal</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-6 hover:border-gaming-gold/30 transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-gaming-gold/20 to-legendary-orange/20 rounded-xl flex items-center justify-center">
                      <Award className="h-6 w-6 text-gaming-gold" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">Recent Milestones</h3>
                      <p className="text-secondary-text text-sm">Platform achievements</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3 p-3 bg-surface-dark/40 rounded-xl">
                      <div className="text-2xl">🎉</div>
                      <div>
                        <p className="text-sm font-medium">500K+ Users Milestone</p>
                        <p className="text-xs text-secondary-text">Reached last week</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-surface-dark/40 rounded-xl">
                      <div className="text-2xl">🚀</div>
                      <div>
                        <p className="text-sm font-medium">New Game Launch</p>
                        <p className="text-xs text-secondary-text">DeFi Defense Wars</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-surface-dark/40 rounded-xl">
                      <div className="text-2xl">💎</div>
                      <div>
                        <p className="text-sm font-medium">$1M+ Rewards Distributed</p>
                        <p className="text-xs text-secondary-text">All-time record</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            {/* Community Tab */}
            <TabsContent value="community" className="space-y-8">
              {/* Enhanced Community Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-accent-cyan/20 to-primary-blue/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-6 w-6 text-accent-cyan" />
                  </div>
                  <div className={`${styles['metric-number']} text-accent-cyan text-3xl mb-2`}>
                    <AnimatedCounter value={communityStats.activePlayers} />
                  </div>
                  <div className={styles['metric-label']}>Active Legends</div>
                  <div className="text-xs text-success-green mt-1">
                    +{Math.floor(communityStats.activePlayers * 0.1)} today
                  </div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-xp-purple/20 to-epic-purple/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <GamepadIcon className="h-6 w-6 text-xp-purple" />
                  </div>
                  <div className={`${styles['metric-number']} text-xp-purple text-3xl mb-2`}>
                    <AnimatedCounter value={communityStats.totalGamesPlayed} />
                  </div>
                  <div className={styles['metric-label']}>Epic Battles</div>
                  <div className="text-xs text-gaming-gold mt-1">
                    All-time record
                  </div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-gaming-gold/20 to-legendary-orange/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <DollarSign className="h-6 w-6 text-gaming-gold" />
                  </div>
                  <div className={`${styles['metric-number']} text-gaming-gold text-2xl mb-2`}>
                    <AnimatedCounter value={communityStats.bnbPool} decimals={0} />
                  </div>
                  <div className={styles['metric-label']}>BNB Pool</div>
                  <div className="text-xs text-success-green mt-1">
                    Growing daily
                  </div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-success-green/20 to-wallet-green/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp className="h-6 w-6 text-success-green" />
                  </div>
                  <div className={`${styles['metric-number']} text-success-green text-3xl mb-2`}>
                    +<AnimatedCounter value={communityStats.weeklyGrowth} decimals={1} />%
                  </div>
                  <div className={styles['metric-label']}>Weekly Growth</div>
                  <div className="text-xs text-accent-cyan mt-1">
                    Trending up
                  </div>
                </div>
              </div>

              {/* Enhanced Leaderboard */}
              <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-6 hover:border-gaming-gold/30 transition-all duration-300">
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-gaming-gold/20 to-legendary-orange/20 rounded-xl flex items-center justify-center">
                      <Trophy className="h-6 w-6 text-gaming-gold" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">Hall of Legends</h3>
                      <p className="text-secondary-text text-sm">Top players this week</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gaming-gold">
                    <Crown className="w-4 h-4" />
                    <span>Elite Rankings</span>
                  </div>
                </div>

                <div className="space-y-4">
                  {communityStats.topPlayers.map((player, index) => (
                    <div
                      key={index}
                      className={`flex items-center justify-between p-4 rounded-xl transition-all duration-300 ${player.position <= 3
                        ? 'bg-gradient-to-r from-gaming-gold/10 to-legendary-orange/10 border border-gaming-gold/30'
                        : 'bg-surface-dark/40 border border-border-light/20'
                        } hover:scale-105`}
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg ${player.position === 1 ? 'bg-gradient-to-br from-gaming-gold to-legendary-orange text-deep-space' :
                          player.position === 2 ? 'bg-gradient-to-br from-secondary-text to-border-light text-deep-space' :
                            player.position === 3 ? 'bg-gradient-to-br from-legendary-orange to-gaming-gold text-deep-space' :
                              'bg-surface-dark border border-border-light text-secondary-text'
                          }`}>
                          {player.position <= 3 ? player.avatar : player.position}
                        </div>
                        <div>
                          <span className="font-semibold text-lg">{player.name}</span>
                          {player.position <= 3 && (
                            <div className="flex items-center gap-1 mt-1">
                              <Sparkles className="w-3 h-3 text-gaming-gold" />
                              <span className="text-xs text-gaming-gold">Legend</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="font-bold text-xl text-accent-cyan">
                          {player.points.toLocaleString()}
                        </span>
                        <p className="text-xs text-secondary-text">JQ Points</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Platform Stats Tab */}
            <TabsContent value="platform" className="space-y-8">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-accent-cyan/20 to-primary-blue/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-6 w-6 text-accent-cyan" />
                  </div>
                  <div className={`${styles['metric-number']} text-accent-cyan text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.totalUsers} />
                  </div>
                  <div className={styles['metric-label']}>Total Gamers</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-success-green/20 to-wallet-green/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Activity className="h-6 w-6 text-success-green" />
                  </div>
                  <div className={`${styles['metric-number']} text-success-green text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.dailyActiveUsers} />
                  </div>
                  <div className={styles['metric-label']}>Daily Active</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-gaming-gold/20 to-legendary-orange/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <DollarSign className="h-6 w-6 text-gaming-gold" />
                  </div>
                  <div className={`${styles['metric-number']} text-gaming-gold text-2xl mb-2`}>
                    $<AnimatedCounter value={platformStats.totalRewards} decimals={0} />
                  </div>
                  <div className={styles['metric-label']}>Total Rewards</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-xp-purple/20 to-epic-purple/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <GamepadIcon className="h-6 w-6 text-xp-purple" />
                  </div>
                  <div className={`${styles['metric-number']} text-xp-purple text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.gamesAvailable} />
                  </div>
                  <div className={styles['metric-label']}>Epic Games</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-blue/20 to-rare-blue/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Target className="h-6 w-6 text-primary-blue" />
                  </div>
                  <div className={`${styles['metric-number']} text-primary-blue text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.partnerships} />
                  </div>
                  <div className={styles['metric-label']}>Partnerships</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-accent-cyan/20 to-success-green/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Coins className="h-6 w-6 text-accent-cyan" />
                  </div>
                  <div className={`${styles['metric-number']} text-accent-cyan text-3xl mb-2`}>
                    <AnimatedCounter value={platformStats.blockchains} />
                  </div>
                  <div className={styles['metric-label']}>Blockchains</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-legendary-orange/20 to-gaming-gold/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp className="h-6 w-6 text-legendary-orange" />
                  </div>
                  <div className={`${styles['metric-number']} text-legendary-orange text-3xl mb-2`}>
                    +<AnimatedCounter value={platformStats.monthlyGrowth} decimals={1} />%
                  </div>
                  <div className={styles['metric-label']}>Monthly Growth</div>
                </div>

                <div className={`${styles['metric-card']} group`}>
                  <div className="w-12 h-12 bg-gradient-to-br from-xp-purple/20 to-epic-purple/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Zap className="h-6 w-6 text-xp-purple" />
                  </div>
                  <div className={`${styles['metric-number']} text-xp-purple text-2xl mb-2`}>
                    <AnimatedCounter value={platformStats.totalTransactions} />
                  </div>
                  <div className={styles['metric-label']}>Transactions</div>
                </div>
              </div>

              {/* Platform Overview */}
              <div className="grid md:grid-cols-2 gap-8">
                <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-6 hover:border-accent-cyan/30 transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-xl flex items-center justify-center">
                      <BarChart3 className="h-6 w-6 text-accent-cyan" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">Platform Health</h3>
                      <p className="text-secondary-text text-sm">System performance metrics</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-surface-dark/40 rounded-xl">
                      <span className="text-sm text-secondary-text">Server Uptime</span>
                      <span className="text-success-green font-bold">99.9%</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-surface-dark/40 rounded-xl">
                      <span className="text-sm text-secondary-text">Transaction Speed</span>
                      <span className="text-accent-cyan font-bold">2s</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-surface-dark/40 rounded-xl">
                      <span className="text-sm text-secondary-text">Network Status</span>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-success-green rounded-full animate-pulse"></div>
                        <span className="text-success-green font-bold">Optimal</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-6 hover:border-gaming-gold/30 transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-gaming-gold/20 to-legendary-orange/20 rounded-xl flex items-center justify-center">
                      <Award className="h-6 w-6 text-gaming-gold" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold">Recent Milestones</h3>
                      <p className="text-secondary-text text-sm">Platform achievements</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3 p-3 bg-surface-dark/40 rounded-xl">
                      <div className="text-2xl">🎉</div>
                      <div>
                        <p className="text-sm font-medium">500K+ Users Milestone</p>
                        <p className="text-xs text-secondary-text">Reached last week</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-surface-dark/40 rounded-xl">
                      <div className="text-2xl">🚀</div>
                      <div>
                        <p className="text-sm font-medium">New Game Launch</p>
                        <p className="text-xs text-secondary-text">DeFi Defense Wars</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 bg-surface-dark/40 rounded-xl">
                      <div className="text-2xl">💎</div>
                      <div>
                        <p className="text-sm font-medium">$1M+ Rewards Distributed</p>
                        <p className="text-xs text-secondary-text">All-time record</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;