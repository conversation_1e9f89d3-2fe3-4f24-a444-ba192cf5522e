import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { WalletProvider } from "@/contexts/WalletContext";
import { GameProvider } from "@/contexts/GameContext";
import Header from "@/components/layout/Header";
import Home from "@/pages/Home/Home";
import Games from "@/pages/Games/Games";
import Dashboard from "@/pages/Dashboard/Dashboard";
import Community from "@/pages/Community/Community";
import Profile from "@/pages/Profile/Profile";
import GamePlay from "@/pages/GamePlay/GamePlay";
import NotFound from "@/pages/NotFound/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <WalletProvider>
      <GameProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <div className="min-h-screen bg-dark-bg">
              <Header />
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/games" element={<Games />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/community" element={<Community />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/game/:gameId" element={<GamePlay />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </div>
          </BrowserRouter>
        </TooltipProvider>
      </GameProvider>
    </WalletProvider>
  </QueryClientProvider>
);

export default App;
