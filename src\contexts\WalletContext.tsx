
import React, { createContext, useContext, useState, ReactNode } from 'react';

interface WalletContextType {
  isConnected: boolean;
  address: string | null;
  balance: string;
  chainId: number | null;
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  switchChain: (chainId: number) => Promise<void>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export const useWallet = () => {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};

interface WalletProviderProps {
  children: ReactNode;
}

export const WalletProvider: React.FC<WalletProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [address, setAddress] = useState<string | null>(null);
  const [balance, setBalance] = useState('0.00');
  const [chainId, setChainId] = useState<number | null>(null);

  const connectWallet = async () => {
    try {
      // Simulate wallet connection
      console.log('Connecting wallet...');
      
      // Simulate async wallet connection
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock wallet data
      setIsConnected(true);
      setAddress('0x742d35CC123456789ABcdEF1234567890AbCdEf');
      setBalance('2.45');
      setChainId(56); // BSC
      
      console.log('Wallet connected successfully');
    } catch (error) {
      console.error('Failed to connect wallet:', error);
    }
  };

  const disconnectWallet = () => {
    setIsConnected(false);
    setAddress(null);
    setBalance('0.00');
    setChainId(null);
    console.log('Wallet disconnected');
  };

  const switchChain = async (newChainId: number) => {
    try {
      console.log(`Switching to chain ${newChainId}...`);
      await new Promise(resolve => setTimeout(resolve, 500));
      setChainId(newChainId);
      console.log(`Switched to chain ${newChainId}`);
    } catch (error) {
      console.error('Failed to switch chain:', error);
    }
  };

  const value: WalletContextType = {
    isConnected,
    address,
    balance,
    chainId,
    connectWallet,
    disconnectWallet,
    switchChain,
  };

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  );
};
